{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["4IF1Fixd0rSEtKj/neZnnrsNrxydvrFto0U/Zx29d3A=", "0IhcLnMOevnzt55e0tJa9sbEKLMnxSgJPLp0JIZh0xc=", "U1aQnzOThdUrra7zLKcaC7bYmd11KjVocrrnIw3DVoI=", "Io7+fY5g2Lvof1EaXCJ2fIVPAzYQFy1DrTciE8ckkLA="], "CachedAssets": {"4IF1Fixd0rSEtKj/neZnnrsNrxydvrFto0U/Zx29d3A=": {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\tzxjg6is5z-sowobu9fea.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "va5o1r7z77", "Integrity": "KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 65109, "LastWriteTime": "2025-07-07T12:28:10.6625593+00:00"}, "0IhcLnMOevnzt55e0tJa9sbEKLMnxSgJPLp0JIZh0xc=": {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rcl324siu", "Integrity": "L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 15475, "LastWriteTime": "2025-07-07T12:28:10.6568254+00:00"}, "U1aQnzOThdUrra7zLKcaC7bYmd11KjVocrrnIw3DVoI=": {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\weg7obbbjn-lp4d2hvui5.gz", "SourceId": "Extensions.MudBlazor.StaticInput", "SourceType": "Package", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/Extensions.MudBlazor.StaticInput", "RelativePath": "NavigationObserver.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vis12zgog2", "Integrity": "p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "FileLength": 1492, "LastWriteTime": "2025-07-07T12:28:10.6558487+00:00"}, "Io7+fY5g2Lvof1EaXCJ2fIVPAzYQFy1DrTciE8ckkLA=": {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\7zrcxa32er-2jeq8efc6q.gz", "SourceId": "BOS.Plant.Dashboard", "SourceType": "Discovered", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/BOS.Plant.Dashboard", "RelativePath": "favicon#[.{fingerprint=2jeq8efc6q}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pkzf8os278", "Integrity": "2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\favicon.ico", "FileLength": 2920, "LastWriteTime": "2025-07-07T12:28:10.6558487+00:00"}}, "CachedCopyCandidates": {}}