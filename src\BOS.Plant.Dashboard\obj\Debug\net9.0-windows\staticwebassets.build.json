{"Version": 1, "Hash": "ObafeOEBg2cfE/gU+fF7g2HlKuNjmg03I+K+4MkM8yE=", "Source": "BOS.Plant.Dashboard", "BasePath": "_content/BOS.Plant.Dashboard", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "BOS.Plant.Dashboard\\wwwroot", "Source": "BOS.Plant.Dashboard", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\", "BasePath": "_content/BOS.Plant.Dashboard", "Pattern": "**"}], "Assets": [{"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rcl324siu", "Integrity": "L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 15475, "LastWriteTime": "2025-07-07T13:27:46+00:00"}, {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\7zrcxa32er-2jeq8efc6q.gz", "SourceId": "BOS.Plant.Dashboard", "SourceType": "Discovered", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/BOS.Plant.Dashboard", "RelativePath": "favicon#[.{fingerprint=2jeq8efc6q}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pkzf8os278", "Integrity": "2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\favicon.ico", "FileLength": 2920, "LastWriteTime": "2025-07-07T13:27:46+00:00"}, {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\tzxjg6is5z-sowobu9fea.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "va5o1r7z77", "Integrity": "KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 65109, "LastWriteTime": "2025-07-07T13:27:46+00:00"}, {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\weg7obbbjn-lp4d2hvui5.gz", "SourceId": "Extensions.MudBlazor.StaticInput", "SourceType": "Package", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/Extensions.MudBlazor.StaticInput", "RelativePath": "NavigationObserver.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vis12zgog2", "Integrity": "p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "FileLength": 1492, "LastWriteTime": "2025-07-07T13:27:46+00:00"}, {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\background.png", "SourceId": "BOS.Plant.Dashboard", "SourceType": "Discovered", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\", "BasePath": "_content/BOS.Plant.Dashboard", "RelativePath": "background#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pqa5jg1248", "Integrity": "X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\background.png", "FileLength": 888155, "LastWriteTime": "2025-07-04T22:52:31+00:00"}, {"Identity": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\favicon.ico", "SourceId": "BOS.Plant.Dashboard", "SourceType": "Discovered", "ContentRoot": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\", "BasePath": "_content/BOS.Plant.Dashboard", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2jeq8efc6q", "Integrity": "8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 15086, "LastWriteTime": "2025-06-26T12:23:32+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "SourceId": "Extensions.MudBlazor.StaticInput", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\", "BasePath": "_content/Extensions.MudBlazor.StaticInput", "RelativePath": "NavigationObserver.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lp4d2hvui5", "Integrity": "5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "FileLength": 6859, "LastWriteTime": "2025-02-20T14:29:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sowobu9fea", "Integrity": "fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 606059, "LastWriteTime": "2025-07-01T21:24:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b8x8f7e52z", "Integrity": "O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 73366, "LastWriteTime": "2025-07-01T21:24:18+00:00"}], "Endpoints": [{"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\weg7obbbjn-lp4d2hvui5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000669792364"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "ETag", "Value": "\"p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6859"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Feb 2025 14:29:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js.gz", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\weg7obbbjn-lp4d2hvui5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.lp4d2hvui5.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6859"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Feb 2025 14:29:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lp4d2hvui5"}, {"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}, {"Name": "label", "Value": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.b8x8f7e52z.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b8x8f7e52z"}, {"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\tzxjg6is5z-sowobu9fea.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015358624"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "65109"}, {"Name": "ETag", "Value": "\"KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "606059"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\tzxjg6is5z-sowobu9fea.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "65109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000064616180"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15475"}, {"Name": "ETag", "Value": "\"L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15475"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.sowobu9fea.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "606059"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sowobu9fea"}, {"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "background.png", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\background.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "888155"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 22:52:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA="}]}, {"Route": "background.pqa5jg1248.png", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\background.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "888155"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 22:52:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pqa5jg1248"}, {"Name": "label", "Value": "background.png"}, {"Name": "integrity", "Value": "sha256-X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA="}]}, {"Route": "favicon.2jeq8efc6q.ico", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\7zrcxa32er-2jeq8efc6q.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000342348511"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2920"}, {"Name": "ETag", "Value": "\"2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}]}, {"Route": "favicon.2jeq8efc6q.ico", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Thu, 26 Jun 2025 12:23:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}]}, {"Route": "favicon.2jeq8efc6q.ico.gz", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\7zrcxa32er-2jeq8efc6q.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2920"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\7zrcxa32er-2jeq8efc6q.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000342348511"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2920"}, {"Name": "ETag", "Value": "\"2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Thu, 26 Jun 2025 12:23:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}]}, {"Route": "favicon.ico.gz", "AssetFile": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\7zrcxa32er-2jeq8efc6q.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2920"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 13:27:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA="}]}]}